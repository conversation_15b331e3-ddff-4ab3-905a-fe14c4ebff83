const { validationResult } = require('express-validator');
const FavoriteCity = require('../models/FavoriteCity');
const User = require('../models/User');

// @desc    Get user's favorite cities
// @route   GET /api/favorites
// @access  Private
const getFavorites = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Please log in to view your favorite cities',
        code: 'AUTH_REQUIRED',
        action: 'login'
      });
    }

    const favorites = await FavoriteCity.findByUser(req.user.id);

    res.json({
      success: true,
      count: favorites.length,
      data: favorites
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Add city to favorites
// @route   POST /api/favorites
// @access  Private
const addFavorite = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Please create an account or log in to save favorite cities',
        code: 'AUTH_REQUIRED',
        action: 'register'
      });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { cityName, country, coordinates, timezone } = req.body;

    // Check if city is already in favorites
    const isAlreadyFavorite = await FavoriteCity.isAlreadyFavorite(
      req.user.id,
      cityName,
      country
    );

    if (isAlreadyFavorite) {
      return res.status(400).json({
        success: false,
        message: 'City is already in your favorites'
      });
    }

    // Create new favorite city
    const favoriteCity = await FavoriteCity.create({
      user: req.user.id,
      cityName,
      country,
      coordinates,
      timezone
    });

    // Add to user's favorites array
    await User.findByIdAndUpdate(
      req.user.id,
      { $push: { favoriteCities: favoriteCity._id } }
    );

    res.status(201).json({
      success: true,
      message: 'City added to favorites successfully',
      data: favoriteCity
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Remove city from favorites
// @route   DELETE /api/favorites/:id
// @access  Private
const removeFavorite = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Please log in to manage your favorite cities',
        code: 'AUTH_REQUIRED',
        action: 'login'
      });
    }

    const { id } = req.params;

    // Find and delete the favorite city
    const favoriteCity = await FavoriteCity.findOneAndDelete({
      _id: id,
      user: req.user.id
    });

    if (!favoriteCity) {
      return res.status(404).json({
        success: false,
        message: 'Favorite city not found'
      });
    }

    // Remove from user's favorites array
    await User.findByIdAndUpdate(
      req.user.id,
      { $pull: { favoriteCities: id } }
    );

    res.json({
      success: true,
      message: 'City removed from favorites successfully'
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update favorite city
// @route   PUT /api/favorites/:id
// @access  Private
const updateFavorite = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { cityName, country, coordinates, timezone } = req.body;

    // Find and update the favorite city
    const favoriteCity = await FavoriteCity.findOneAndUpdate(
      { _id: id, user: req.user.id },
      { cityName, country, coordinates, timezone },
      { new: true, runValidators: true }
    );

    if (!favoriteCity) {
      return res.status(404).json({
        success: false,
        message: 'Favorite city not found'
      });
    }

    res.json({
      success: true,
      message: 'Favorite city updated successfully',
      data: favoriteCity
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single favorite city
// @route   GET /api/favorites/:id
// @access  Private
const getFavorite = async (req, res, next) => {
  try {
    const { id } = req.params;

    const favoriteCity = await FavoriteCity.findOne({
      _id: id,
      user: req.user.id
    });

    if (!favoriteCity) {
      return res.status(404).json({
        success: false,
        message: 'Favorite city not found'
      });
    }

    res.json({
      success: true,
      data: favoriteCity
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getFavorites,
  addFavorite,
  removeFavorite,
  updateFavorite,
  getFavorite
};
