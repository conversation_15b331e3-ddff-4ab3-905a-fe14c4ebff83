import React from 'react';
import './LoadingSkeleton.css';

const LoadingSkeleton = ({ type = 'card', count = 1 }) => {
  const renderWeatherCardSkeleton = () => (
    <div className="skeleton-card weather-card-skeleton">
      <div className="skeleton-header">
        <div className="skeleton-text skeleton-title"></div>
        <div className="skeleton-circle skeleton-favorite"></div>
      </div>
      <div className="skeleton-main">
        <div className="skeleton-circle skeleton-weather-icon"></div>
        <div className="skeleton-temp-info">
          <div className="skeleton-text skeleton-temp"></div>
          <div className="skeleton-text skeleton-desc"></div>
        </div>
      </div>
      <div className="skeleton-details">
        <div className="skeleton-detail-row">
          <div className="skeleton-detail-item">
            <div className="skeleton-circle skeleton-icon"></div>
            <div className="skeleton-text skeleton-label"></div>
          </div>
          <div className="skeleton-detail-item">
            <div className="skeleton-circle skeleton-icon"></div>
            <div className="skeleton-text skeleton-label"></div>
          </div>
        </div>
        <div className="skeleton-detail-row">
          <div className="skeleton-detail-item">
            <div className="skeleton-circle skeleton-icon"></div>
            <div className="skeleton-text skeleton-label"></div>
          </div>
          <div className="skeleton-detail-item">
            <div className="skeleton-circle skeleton-icon"></div>
            <div className="skeleton-text skeleton-label"></div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCompactCardSkeleton = () => (
    <div className="skeleton-card compact-card-skeleton">
      <div className="skeleton-header">
        <div className="skeleton-text skeleton-city"></div>
        <div className="skeleton-circle skeleton-favorite-small"></div>
      </div>
      <div className="skeleton-main-compact">
        <div className="skeleton-circle skeleton-icon-small"></div>
        <div className="skeleton-temp-compact">
          <div className="skeleton-text skeleton-temp-small"></div>
          <div className="skeleton-text skeleton-desc-small"></div>
        </div>
      </div>
      <div className="skeleton-details-compact">
        <div className="skeleton-detail-compact">
          <div className="skeleton-circle skeleton-icon-tiny"></div>
          <div className="skeleton-text skeleton-value-small"></div>
        </div>
        <div className="skeleton-detail-compact">
          <div className="skeleton-circle skeleton-icon-tiny"></div>
          <div className="skeleton-text skeleton-value-small"></div>
        </div>
      </div>
    </div>
  );

  const renderSearchSkeleton = () => (
    <div className="skeleton-search">
      <div className="skeleton-search-bar">
        <div className="skeleton-circle skeleton-search-icon"></div>
        <div className="skeleton-text skeleton-search-input"></div>
        <div className="skeleton-text skeleton-search-btn"></div>
      </div>
    </div>
  );

  const renderListSkeleton = () => (
    <div className="skeleton-list">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className="skeleton-list-item">
          <div className="skeleton-circle skeleton-list-icon"></div>
          <div className="skeleton-list-content">
            <div className="skeleton-text skeleton-list-title"></div>
            <div className="skeleton-text skeleton-list-subtitle"></div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderSkeleton = () => {
    switch (type) {
      case 'weather-card':
        return renderWeatherCardSkeleton();
      case 'compact-card':
        return renderCompactCardSkeleton();
      case 'search':
        return renderSearchSkeleton();
      case 'list':
        return renderListSkeleton();
      default:
        return renderWeatherCardSkeleton();
    }
  };

  return (
    <div className="loading-skeleton">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index}>
          {renderSkeleton()}
        </div>
      ))}
    </div>
  );
};

export default LoadingSkeleton;
