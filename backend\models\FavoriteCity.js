const mongoose = require('mongoose');

const favoriteCitySchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  cityName: {
    type: String,
    required: [true, 'City name is required'],
    trim: true,
    minlength: [1, 'City name cannot be empty'],
    maxlength: [100, 'City name cannot exceed 100 characters']
  },
  country: {
    type: String,
    required: [true, 'Country is required'],
    trim: true,
    maxlength: [100, 'Country name cannot exceed 100 characters']
  },
  coordinates: {
    lat: {
      type: Number,
      required: true,
      min: [-90, 'Latitude must be between -90 and 90'],
      max: [90, 'Latitude must be between -90 and 90']
    },
    lon: {
      type: Number,
      required: true,
      min: [-180, 'Longitude must be between -180 and 180'],
      max: [180, 'Longitude must be between -180 and 180']
    }
  },
  timezone: {
    type: String,
    default: 'UTC'
  },
  addedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create compound index to prevent duplicate cities for the same user
favoriteCitySchema.index({ user: 1, cityName: 1, country: 1 }, { unique: true });

// Instance method to format city display name
favoriteCitySchema.methods.getDisplayName = function() {
  return `${this.cityName}, ${this.country}`;
};

// Static method to find user's favorite cities
favoriteCitySchema.statics.findByUser = function(userId) {
  return this.find({ user: userId }).sort({ addedAt: -1 });
};

// Static method to check if city is already in user's favorites
favoriteCitySchema.statics.isAlreadyFavorite = async function(userId, cityName, country) {
  const existing = await this.findOne({
    user: userId,
    cityName: { $regex: new RegExp(`^${cityName}$`, 'i') },
    country: { $regex: new RegExp(`^${country}$`, 'i') }
  });
  return !!existing;
};

module.exports = mongoose.model('FavoriteCity', favoriteCitySchema);
