const axios = require('axios');
const { validationResult } = require('express-validator');

// @desc    Get current weather by city name
// @route   GET /api/weather/current/:city
// @access  Private
const getCurrentWeather = async (req, res, next) => {
  try {
    const { city } = req.params;
    
    if (!city) {
      return res.status(400).json({
        success: false,
        message: 'City name is required'
      });
    }

    const apiKey = process.env.WEATHER_API_KEY;
    const apiUrl = process.env.WEATHER_API_URL;

    if (!apiKey) {
      return res.status(500).json({
        success: false,
        message: 'Weather API key not configured'
      });
    }

    // Get current weather data
    const weatherResponse = await axios.get(
      `${apiUrl}/weather?q=${encodeURIComponent(city)}&appid=${apiKey}&units=metric`
    );

    const weatherData = weatherResponse.data;

    // Format the response
    const formattedWeather = {
      city: weatherData.name,
      country: weatherData.sys.country,
      coordinates: {
        lat: weatherData.coord.lat,
        lon: weatherData.coord.lon
      },
      weather: {
        main: weatherData.weather[0].main,
        description: weatherData.weather[0].description,
        icon: weatherData.weather[0].icon
      },
      temperature: {
        current: Math.round(weatherData.main.temp),
        feelsLike: Math.round(weatherData.main.feels_like),
        min: Math.round(weatherData.main.temp_min),
        max: Math.round(weatherData.main.temp_max)
      },
      humidity: weatherData.main.humidity,
      pressure: weatherData.main.pressure,
      visibility: weatherData.visibility ? Math.round(weatherData.visibility / 1000) : null,
      wind: {
        speed: weatherData.wind.speed,
        direction: weatherData.wind.deg
      },
      clouds: weatherData.clouds.all,
      sunrise: new Date(weatherData.sys.sunrise * 1000).toISOString(),
      sunset: new Date(weatherData.sys.sunset * 1000).toISOString(),
      timezone: weatherData.timezone,
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: formattedWeather
    });

  } catch (error) {
    console.error('Weather API Error:', error.response?.data || error.message);
    
    if (error.response?.status === 404) {
      return res.status(404).json({
        success: false,
        message: 'City not found'
      });
    }
    
    if (error.response?.status === 401) {
      return res.status(500).json({
        success: false,
        message: 'Weather API authentication failed'
      });
    }

    next(error);
  }
};

// @desc    Get weather by coordinates
// @route   GET /api/weather/coordinates
// @access  Private
const getWeatherByCoordinates = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { lat, lon } = req.query;

    const apiKey = process.env.WEATHER_API_KEY;
    const apiUrl = process.env.WEATHER_API_URL;

    // Get current weather data by coordinates
    const weatherResponse = await axios.get(
      `${apiUrl}/weather?lat=${lat}&lon=${lon}&appid=${apiKey}&units=metric`
    );

    const weatherData = weatherResponse.data;

    // Format the response (same as getCurrentWeather)
    const formattedWeather = {
      city: weatherData.name,
      country: weatherData.sys.country,
      coordinates: {
        lat: weatherData.coord.lat,
        lon: weatherData.coord.lon
      },
      weather: {
        main: weatherData.weather[0].main,
        description: weatherData.weather[0].description,
        icon: weatherData.weather[0].icon
      },
      temperature: {
        current: Math.round(weatherData.main.temp),
        feelsLike: Math.round(weatherData.main.feels_like),
        min: Math.round(weatherData.main.temp_min),
        max: Math.round(weatherData.main.temp_max)
      },
      humidity: weatherData.main.humidity,
      pressure: weatherData.main.pressure,
      visibility: weatherData.visibility ? Math.round(weatherData.visibility / 1000) : null,
      wind: {
        speed: weatherData.wind.speed,
        direction: weatherData.wind.deg
      },
      clouds: weatherData.clouds.all,
      sunrise: new Date(weatherData.sys.sunrise * 1000).toISOString(),
      sunset: new Date(weatherData.sys.sunset * 1000).toISOString(),
      timezone: weatherData.timezone,
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: formattedWeather
    });

  } catch (error) {
    console.error('Weather API Error:', error.response?.data || error.message);
    next(error);
  }
};

// @desc    Search cities
// @route   GET /api/weather/search/:query
// @access  Private
const searchCities = async (req, res, next) => {
  try {
    const { query } = req.params;
    
    if (!query || query.length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters long'
      });
    }

    const apiKey = process.env.WEATHER_API_KEY;

    // Use geocoding API to search for cities
    const geocodingResponse = await axios.get(
      `http://api.openweathermap.org/geo/1.0/direct?q=${encodeURIComponent(query)}&limit=5&appid=${apiKey}`
    );

    const cities = geocodingResponse.data.map(city => ({
      name: city.name,
      country: city.country,
      state: city.state,
      coordinates: {
        lat: city.lat,
        lon: city.lon
      }
    }));

    res.json({
      success: true,
      data: cities
    });

  } catch (error) {
    console.error('City search error:', error.response?.data || error.message);
    next(error);
  }
};

module.exports = {
  getCurrentWeather,
  getWeatherByCoordinates,
  searchCities
};
