.favorites-list {
  width: 100%;
}

.favorites-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1.5rem;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.refresh-btn svg {
  font-size: 1rem;
}

.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  justify-items: center;
}

.favorites-empty {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 4rem;
  color: #e2e8f0;
  margin-bottom: 1.5rem;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.empty-description {
  color: #718096;
  font-size: 1rem;
  line-height: 1.6;
  max-width: 400px;
  margin: 0 auto 2rem;
}

.empty-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: 300px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  color: #4a5568;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.feature-item span {
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .favorites-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .favorites-header {
    justify-content: center;
    margin-bottom: 1rem;
  }
  
  .refresh-btn {
    padding: 0.625rem 1rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .favorites-empty {
    padding: 3rem 1.5rem;
  }
  
  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }
  
  .empty-title {
    font-size: 1.25rem;
  }
  
  .empty-description {
    font-size: 0.9rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .favorites-empty {
    background: #2d3748;
    color: white;
  }
  
  .empty-icon {
    color: #4a5568;
  }
  
  .empty-title {
    color: #e2e8f0;
  }
  
  .empty-description {
    color: #a0aec0;
  }
}
