const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Optional authentication middleware - doesn't block if no token provided
const optionalAuth = async (req, res, next) => {
  try {
    // Get token from header
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      // No token provided, continue as guest user
      req.user = null;
      req.isAuthenticated = false;
      return next();
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from database
      const user = await User.findById(decoded.id).select('-password');
      
      if (!user) {
        // Invalid token, continue as guest
        req.user = null;
        req.isAuthenticated = false;
        return next();
      }

      // Add user to request object
      req.user = user;
      req.isAuthenticated = true;
      next();
    } catch (tokenError) {
      // Invalid or expired token, continue as guest
      req.user = null;
      req.isAuthenticated = false;
      next();
    }
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    // On any error, continue as guest
    req.user = null;
    req.isAuthenticated = false;
    next();
  }
};

module.exports = optionalAuth;
