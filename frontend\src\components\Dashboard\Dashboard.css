.dashboard {
  min-height: calc(100vh - 60px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.dashboard-container {
  position: relative;
  z-index: 1;
  padding: 3rem 1rem;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 4rem;
  color: white;
}

.dashboard-title {
  font-size: 3rem;
  font-weight: 800;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.dashboard-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto 2rem;
  line-height: 1.6;
  font-weight: 400;
}

.guest-banner {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem 2rem;
  margin: 2rem auto 0;
  max-width: 600px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.guest-banner p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: white;
  text-align: center;
}

.search-section {
  margin-bottom: 4rem;
}

.current-weather-section {
  margin-bottom: 4rem;
}

.favorites-section {
  margin-bottom: 3rem;
}

.guest-section {
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 2rem;
  text-align: center;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.01em;
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard {
    padding: 1.5rem 0;
  }
  
  .dashboard-container {
    padding: 0 0.5rem;
  }
  
  .dashboard-header {
    margin-bottom: 2rem;
  }
  
  .dashboard-title {
    font-size: 2rem;
  }
  
  .dashboard-subtitle {
    font-size: 1rem;
  }
  
  .section-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .search-section,
  .current-weather-section,
  .favorites-section {
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: 1rem 0;
  }
  
  .dashboard-title {
    font-size: 1.75rem;
  }
  
  .dashboard-subtitle {
    font-size: 0.95rem;
  }
  
  .section-title {
    font-size: 1.25rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dashboard {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
  
  .dashboard-title {
    color: white;
  }
  
  .dashboard-subtitle {
    color: #a0aec0;
  }
  
  .section-title {
    color: #e2e8f0;
  }
}
