import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (name, email, password) =>
    api.post('/auth/register', { name, email, password }),
  
  login: (email, password) =>
    api.post('/auth/login', { email, password }),
  
  getMe: () =>
    api.get('/auth/me'),
  
  updateProfile: (userData) =>
    api.put('/auth/profile', userData)
};

// Weather API
export const weatherAPI = {
  getCurrentWeather: (city) =>
    api.get(`/weather/current/${encodeURIComponent(city)}`),
  
  getWeatherByCoordinates: (lat, lon) =>
    api.get(`/weather/coordinates?lat=${lat}&lon=${lon}`),
  
  searchCities: (query) =>
    api.get(`/weather/search/${encodeURIComponent(query)}`)
};

// Favorites API
export const favoritesAPI = {
  getFavorites: () =>
    api.get('/favorites'),
  
  addFavorite: (cityData) =>
    api.post('/favorites', cityData),
  
  removeFavorite: (id) =>
    api.delete(`/favorites/${id}`),
  
  updateFavorite: (id, cityData) =>
    api.put(`/favorites/${id}`, cityData),
  
  getFavorite: (id) =>
    api.get(`/favorites/${id}`)
};

export default api;
