import React from 'react';
import { 
  <PERSON>H<PERSON>t, 
  <PERSON>Wind, 
  FiDroplet, 
  FiEye, 
  FiThermometer,
  FiSunrise,
  FiSunset,
  FiMapPin
} from 'react-icons/fi';
import './WeatherCard.css';

const WeatherCard = ({ 
  weather, 
  onAddToFavorites, 
  onRemoveFromFavorites, 
  isInFavorites, 
  favoriteId,
  compact = false 
}) => {
  const handleFavoriteToggle = () => {
    if (isInFavorites) {
      onRemoveFromFavorites(favoriteId);
    } else {
      onAddToFavorites(weather);
    }
  };

  const getWeatherIconUrl = (iconCode) => {
    return `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
  };

  const formatTime = (isoString) => {
    return new Date(isoString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const getWindDirection = (degrees) => {
    const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  };

  const getHumidityLevel = (humidity) => {
    if (humidity < 30) return { level: 'Low', color: '#fbbf24' };
    if (humidity < 60) return { level: 'Moderate', color: '#10b981' };
    return { level: 'High', color: '#3b82f6' };
  };

  const getWindSpeedLevel = (speed) => {
    if (speed < 3) return { level: 'Light', color: '#10b981' };
    if (speed < 8) return { level: 'Moderate', color: '#fbbf24' };
    if (speed < 15) return { level: 'Strong', color: '#f59e0b' };
    return { level: 'Very Strong', color: '#ef4444' };
  };

  if (compact) {
    return (
      <div className="weather-card compact">
        <div className="weather-header">
          <div className="location-info">
            <h3 className="city-name">{weather.city}</h3>
            <p className="country-name">{weather.country}</p>
          </div>
          <button
            onClick={handleFavoriteToggle}
            className={`favorite-btn ${isInFavorites ? 'active' : ''}`}
            title={isInFavorites ? 'Remove from favorites' : 'Add to favorites'}
          >
            <FiHeart />
          </button>
        </div>

        <div className="weather-main compact">
          <div className="weather-icon-temp">
            <img
              src={getWeatherIconUrl(weather.weather.icon)}
              alt={weather.weather.description}
              className="weather-icon"
            />
            <div className="temperature-info">
              <span className="current-temp">{weather.temperature.current}°C</span>
              <span className="weather-desc">{weather.weather.description}</span>
            </div>
          </div>
        </div>

        <div className="weather-details compact">
          <div className="detail-item">
            <FiDroplet className="detail-icon" />
            <div className="detail-content">
              <span className="detail-value">{weather.humidity}%</span>
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{
                    width: `${weather.humidity}%`,
                    backgroundColor: getHumidityLevel(weather.humidity).color
                  }}
                ></div>
              </div>
            </div>
          </div>
          <div className="detail-item">
            <FiWind className="detail-icon" />
            <div className="detail-content">
              <span className="detail-value">{weather.wind.speed} m/s</span>
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{
                    width: `${Math.min((weather.wind.speed / 20) * 100, 100)}%`,
                    backgroundColor: getWindSpeedLevel(weather.wind.speed).color
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="weather-card">
      <div className="weather-header">
        <div className="location-info">
          <h2 className="city-name">
            <FiMapPin className="location-icon" />
            {weather.city}
          </h2>
          <p className="country-name">{weather.country}</p>
          <p className="coordinates">
            {weather.coordinates.lat.toFixed(2)}°, {weather.coordinates.lon.toFixed(2)}°
          </p>
        </div>
        <button
          onClick={handleFavoriteToggle}
          className={`favorite-btn ${isInFavorites ? 'active' : ''}`}
          title={isInFavorites ? 'Remove from favorites' : 'Add to favorites'}
        >
          <FiHeart />
        </button>
      </div>

      <div className="weather-main">
        <div className="weather-icon-container">
          <img
            src={getWeatherIconUrl(weather.weather.icon)}
            alt={weather.weather.description}
            className="weather-icon large"
          />
        </div>
        <div className="temperature-info">
          <span className="current-temp">{weather.temperature.current}°C</span>
          <span className="feels-like">Feels like {weather.temperature.feelsLike}°C</span>
          <span className="weather-desc">{weather.weather.description}</span>
          <div className="temp-range">
            <span className="temp-min">L: {weather.temperature.min}°C</span>
            <span className="temp-max">H: {weather.temperature.max}°C</span>
          </div>
        </div>
      </div>

      <div className="weather-details">
        <div className="detail-row">
          <div className="detail-item">
            <FiDroplet className="detail-icon" />
            <div className="detail-content">
              <span className="detail-label">Humidity</span>
              <span className="detail-value">{weather.humidity}%</span>
            </div>
          </div>
          <div className="detail-item">
            <FiWind className="detail-icon" />
            <div className="detail-content">
              <span className="detail-label">Wind</span>
              <span className="detail-value">
                {weather.wind.speed} m/s {getWindDirection(weather.wind.direction)}
              </span>
            </div>
          </div>
        </div>

        <div className="detail-row">
          <div className="detail-item">
            <FiThermometer className="detail-icon" />
            <div className="detail-content">
              <span className="detail-label">Pressure</span>
              <span className="detail-value">{weather.pressure} hPa</span>
            </div>
          </div>
          {weather.visibility && (
            <div className="detail-item">
              <FiEye className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">Visibility</span>
                <span className="detail-value">{weather.visibility} km</span>
              </div>
            </div>
          )}
        </div>

        <div className="detail-row">
          <div className="detail-item">
            <FiSunrise className="detail-icon" />
            <div className="detail-content">
              <span className="detail-label">Sunrise</span>
              <span className="detail-value">{formatTime(weather.sunrise)}</span>
            </div>
          </div>
          <div className="detail-item">
            <FiSunset className="detail-icon" />
            <div className="detail-content">
              <span className="detail-label">Sunset</span>
              <span className="detail-value">{formatTime(weather.sunset)}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="weather-footer">
        <p className="last-updated">
          Last updated: {new Date(weather.lastUpdated).toLocaleString()}
        </p>
      </div>
    </div>
  );
};

export default WeatherCard;
