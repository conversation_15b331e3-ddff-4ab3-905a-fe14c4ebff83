import React from 'react';
import { FiRefreshCw, FiHeart } from 'react-icons/fi';
import WeatherCard from './WeatherCard';
import LoadingSpinner from '../UI/LoadingSpinner';
import './FavoritesList.css';

const FavoritesList = ({ favorites, onRemoveFromFavorites, onRefresh }) => {
  if (favorites.length === 0) {
    return (
      <div className="favorites-empty">
        <div className="empty-icon">
          <FiHeart />
        </div>
        <h3 className="empty-title">No Favorite Cities Yet</h3>
        <p className="empty-description">
          Search for cities and click the heart icon to add them to your favorites.
        </p>
        <div className="empty-features">
          <div className="feature-item">
            <span>⚡ Quick access to weather</span>
          </div>
          <div className="feature-item">
            <span>🔄 Auto-refresh data</span>
          </div>
          <div className="feature-item">
            <span>📱 Sync across devices</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="favorites-list">
      <div className="favorites-header">
        <button
          onClick={onRefresh}
          className="refresh-btn"
          title="Refresh weather data"
        >
          <FiRefreshCw />
          Refresh
        </button>
      </div>

      <div className="favorites-grid">
        {favorites.map((weather) => (
          <WeatherCard
            key={weather.favoriteId}
            weather={weather}
            onRemoveFromFavorites={onRemoveFromFavorites}
            isInFavorites={true}
            favoriteId={weather.favoriteId}
            compact={true}
          />
        ))}
      </div>
    </div>
  );
};

export default FavoritesList;
