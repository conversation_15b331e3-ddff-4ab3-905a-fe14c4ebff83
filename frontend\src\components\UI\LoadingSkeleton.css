.loading-skeleton {
  width: 100%;
}

/* Base skeleton styles */
.skeleton-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.skeleton-text {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 8px;
  height: 1rem;
}

.skeleton-circle {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 50%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Weather card skeleton */
.weather-card-skeleton {
  max-width: 600px;
  margin: 0 auto;
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.skeleton-title {
  width: 200px;
  height: 1.5rem;
}

.skeleton-favorite {
  width: 48px;
  height: 48px;
}

.skeleton-main {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.skeleton-weather-icon {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
}

.skeleton-temp-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-temp {
  width: 150px;
  height: 3rem;
}

.skeleton-desc {
  width: 180px;
  height: 1.25rem;
}

.skeleton-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.skeleton-detail-row {
  display: flex;
  gap: 2rem;
}

.skeleton-detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.skeleton-icon {
  width: 20px;
  height: 20px;
}

.skeleton-label {
  width: 80px;
  height: 1rem;
}

/* Compact card skeleton */
.compact-card-skeleton {
  max-width: 350px;
  padding: 1.5rem;
}

.skeleton-city {
  width: 120px;
  height: 1.25rem;
}

.skeleton-favorite-small {
  width: 32px;
  height: 32px;
}

.skeleton-main-compact {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.skeleton-icon-small {
  width: 80px;
  height: 80px;
}

.skeleton-temp-compact {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-temp-small {
  width: 80px;
  height: 2rem;
}

.skeleton-desc-small {
  width: 100px;
  height: 1rem;
}

.skeleton-details-compact {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
}

.skeleton-detail-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.skeleton-icon-tiny {
  width: 16px;
  height: 16px;
}

.skeleton-value-small {
  width: 40px;
  height: 0.875rem;
}

/* Search skeleton */
.skeleton-search {
  max-width: 600px;
  margin: 0 auto 2rem;
}

.skeleton-search-bar {
  background: white;
  border-radius: 12px;
  padding: 0.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.skeleton-search-icon {
  width: 20px;
  height: 20px;
  margin-left: 0.5rem;
}

.skeleton-search-input {
  flex: 1;
  height: 1.25rem;
}

.skeleton-search-btn {
  width: 80px;
  height: 2.5rem;
  border-radius: 8px;
}

/* List skeleton */
.skeleton-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.skeleton-list-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  gap: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-list-item:last-child {
  border-bottom: none;
}

.skeleton-list-icon {
  width: 24px;
  height: 24px;
}

.skeleton-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-list-title {
  width: 150px;
  height: 1rem;
}

.skeleton-list-subtitle {
  width: 100px;
  height: 0.875rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .skeleton-card {
    padding: 1.5rem;
  }
  
  .skeleton-main {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .skeleton-detail-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .skeleton-temp {
    width: 120px;
    height: 2.5rem;
  }
  
  .skeleton-weather-icon {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .skeleton-card {
    padding: 1rem;
  }
  
  .skeleton-title {
    width: 150px;
    height: 1.25rem;
  }
  
  .skeleton-temp {
    width: 100px;
    height: 2rem;
  }
  
  .skeleton-weather-icon {
    width: 80px;
    height: 80px;
  }
  
  .skeleton-favorite {
    width: 40px;
    height: 40px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .skeleton-card {
    background: #2d3748;
  }
  
  .skeleton-text,
  .skeleton-circle {
    background: linear-gradient(90deg, #4a5568 25%, #718096 50%, #4a5568 75%);
    background-size: 200% 100%;
  }
  
  .skeleton-search-bar {
    background: #2d3748;
  }
  
  .skeleton-list {
    background: #2d3748;
  }
  
  .skeleton-list-item {
    border-bottom-color: #4a5568;
  }
}
