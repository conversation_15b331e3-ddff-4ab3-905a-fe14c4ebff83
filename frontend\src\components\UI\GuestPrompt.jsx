import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>Heart, FiUser, FiStar } from 'react-icons/fi';
import './GuestPrompt.css';

const GuestPrompt = ({ 
  title = "Save Your Favorite Cities", 
  message = "Create an account to save and manage your favorite weather locations",
  action = "favorites",
  compact = false 
}) => {
  const getIcon = () => {
    switch (action) {
      case 'favorites':
        return <FiHeart />;
      case 'profile':
        return <FiUser />;
      default:
        return <FiStar />;
    }
  };

  const getActionText = () => {
    switch (action) {
      case 'favorites':
        return 'to save favorite cities';
      case 'profile':
        return 'to manage your profile';
      default:
        return 'for full access';
    }
  };

  if (compact) {
    return (
      <div className="guest-prompt compact">
        <div className="prompt-icon">
          {getIcon()}
        </div>
        <div className="prompt-content">
          <p className="prompt-message">{message}</p>
          <div className="prompt-actions">
            <Link to="/register" className="prompt-btn primary">
              Sign Up
            </Link>
            <Link to="/login" className="prompt-btn secondary">
              Login
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="guest-prompt">
      <div className="prompt-header">
        <div className="prompt-icon">
          {getIcon()}
        </div>
        <h3 className="prompt-title">{title}</h3>
      </div>
      
      <p className="prompt-message">{message}</p>
      
      <div className="prompt-features">
        <div className="feature-item">
          <FiHeart className="feature-icon" />
          <span>Save favorite cities</span>
        </div>
        <div className="feature-item">
          <FiUser className="feature-icon" />
          <span>Personalized dashboard</span>
        </div>
        <div className="feature-item">
          <FiStar className="feature-icon" />
          <span>Weather alerts</span>
        </div>
      </div>
      
      <div className="prompt-actions">
        <Link to="/register" className="prompt-btn primary">
          Create Free Account
        </Link>
        <Link to="/login" className="prompt-btn secondary">
          Already have an account? Login
        </Link>
      </div>
      
      <p className="prompt-note">
        It's free and takes less than a minute!
      </p>
    </div>
  );
};

export default GuestPrompt;
