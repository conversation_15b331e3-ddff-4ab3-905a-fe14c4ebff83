const express = require('express');
const router = express.Router();

const {
  getFavorites,
  addFavorite,
  removeFavorite,
  updateFavorite,
  getFavorite
} = require('../controllers/favoritesController');

const auth = require('../middleware/auth');
const {
  validateAddFavorite,
  validateUpdateFavorite,
  validateObjectId
} = require('../middleware/validation');

// @route   GET /api/favorites
// @desc    Get user's favorite cities
// @access  Private
router.get('/', auth, getFavorites);

// @route   POST /api/favorites
// @desc    Add city to favorites
// @access  Private
router.post('/', auth, validateAddFavorite, addFavorite);

// @route   GET /api/favorites/:id
// @desc    Get single favorite city
// @access  Private
router.get('/:id', auth, validateObjectId, getFavorite);

// @route   PUT /api/favorites/:id
// @desc    Update favorite city
// @access  Private
router.put('/:id', auth, validateObjectId, validateUpdateFavorite, updateFavorite);

// @route   DELETE /api/favorites/:id
// @desc    Remove city from favorites
// @access  Private
router.delete('/:id', auth, validateObjectId, removeFavorite);

module.exports = router;
