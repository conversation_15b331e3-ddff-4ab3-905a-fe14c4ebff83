const express = require('express');
const router = express.Router();

const {
  getCurrentWeather,
  getWeatherByCoordinates,
  searchCities
} = require('../controllers/weatherController');

const auth = require('../middleware/auth');
const {
  validateWeatherCoordinates,
  validateCitySearch,
  validateCityName
} = require('../middleware/validation');

// @route   GET /api/weather/current/:city
// @desc    Get current weather by city name
// @access  Private
router.get('/current/:city', auth, validateCityName, getCurrentWeather);

// @route   GET /api/weather/coordinates
// @desc    Get weather by coordinates
// @access  Private
router.get('/coordinates', auth, validateWeatherCoordinates, getWeatherByCoordinates);

// @route   GET /api/weather/search/:query
// @desc    Search cities
// @access  Private
router.get('/search/:query', auth, validateCitySearch, searchCities);

module.exports = router;
