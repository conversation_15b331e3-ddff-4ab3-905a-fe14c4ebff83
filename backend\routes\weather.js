const express = require('express');
const router = express.Router();

const {
  getCurrentWeather,
  getWeatherByCoordinates,
  searchCities
} = require('../controllers/weatherController');

const auth = require('../middleware/auth');
const {
  validateWeatherCoordinates,
  validateCitySearch,
  validateCityName
} = require('../middleware/validation');

// @route   GET /api/weather/current/:city
// @desc    Get current weather by city name
// @access  Public
router.get('/current/:city', validateCityName, getCurrentWeather);

// @route   GET /api/weather/coordinates
// @desc    Get weather by coordinates
// @access  Public
router.get('/coordinates', validateWeatherCoordinates, getWeatherByCoordinates);

// @route   GET /api/weather/search/:query
// @desc    Search cities
// @access  Public
router.get('/search/:query', validateCitySearch, searchCities);

module.exports = router;
