// THIS FILE IS AUTO GENERATED
import { IconTree, IconType } from '../lib'
export declare const CgAbstract: IconType;
export declare const CgAddR: IconType;
export declare const CgAdd: IconType;
export declare const CgAdidas: IconType;
export declare const CgAirplane: IconType;
export declare const CgAlarm: IconType;
export declare const CgAlbum: IconType;
export declare const CgAlignBottom: IconType;
export declare const CgAlignCenter: IconType;
export declare const CgAlignLeft: IconType;
export declare const CgAlignMiddle: IconType;
export declare const CgAlignRight: IconType;
export declare const CgAlignTop: IconType;
export declare const CgAnchor: IconType;
export declare const CgAppleWatch: IconType;
export declare const CgArrangeBack: IconType;
export declare const CgArrangeFront: IconType;
export declare const CgArrowAlignH: IconType;
export declare const CgArrowAlignV: IconType;
export declare const CgArrowBottomLeftO: IconType;
export declare const CgArrowBottomLeftR: IconType;
export declare const CgArrowBottomLeft: IconType;
export declare const CgArrowBottomRightO: IconType;
export declare const CgArrowBottomRightR: IconType;
export declare const CgArrowBottomRight: IconType;
export declare const CgArrowDownO: IconType;
export declare const CgArrowDownR: IconType;
export declare const CgArrowDown: IconType;
export declare const CgArrowLeftO: IconType;
export declare const CgArrowLeftR: IconType;
export declare const CgArrowLeft: IconType;
export declare const CgArrowLongDownC: IconType;
export declare const CgArrowLongDownE: IconType;
export declare const CgArrowLongDownL: IconType;
export declare const CgArrowLongDownR: IconType;
export declare const CgArrowLongDown: IconType;
export declare const CgArrowLongLeftC: IconType;
export declare const CgArrowLongLeftE: IconType;
export declare const CgArrowLongLeftL: IconType;
export declare const CgArrowLongLeftR: IconType;
export declare const CgArrowLongLeft: IconType;
export declare const CgArrowLongRightC: IconType;
export declare const CgArrowLongRightE: IconType;
export declare const CgArrowLongRightL: IconType;
export declare const CgArrowLongRightR: IconType;
export declare const CgArrowLongRight: IconType;
export declare const CgArrowLongUpC: IconType;
export declare const CgArrowLongUpE: IconType;
export declare const CgArrowLongUpL: IconType;
export declare const CgArrowLongUpR: IconType;
export declare const CgArrowLongUp: IconType;
export declare const CgArrowRightO: IconType;
export declare const CgArrowRightR: IconType;
export declare const CgArrowRight: IconType;
export declare const CgArrowTopLeftO: IconType;
export declare const CgArrowTopLeftR: IconType;
export declare const CgArrowTopLeft: IconType;
export declare const CgArrowTopRightO: IconType;
export declare const CgArrowTopRightR: IconType;
export declare const CgArrowTopRight: IconType;
export declare const CgArrowUpO: IconType;
export declare const CgArrowUpR: IconType;
export declare const CgArrowUp: IconType;
export declare const CgArrowsBreakeH: IconType;
export declare const CgArrowsBreakeV: IconType;
export declare const CgArrowsExchangeAltV: IconType;
export declare const CgArrowsExchangeAlt: IconType;
export declare const CgArrowsExchangeV: IconType;
export declare const CgArrowsExchange: IconType;
export declare const CgArrowsExpandDownLeft: IconType;
export declare const CgArrowsExpandDownRight: IconType;
export declare const CgArrowsExpandLeftAlt: IconType;
export declare const CgArrowsExpandLeft: IconType;
export declare const CgArrowsExpandRightAlt: IconType;
export declare const CgArrowsExpandRight: IconType;
export declare const CgArrowsExpandUpLeft: IconType;
export declare const CgArrowsExpandUpRight: IconType;
export declare const CgArrowsHAlt: IconType;
export declare const CgArrowsH: IconType;
export declare const CgArrowsMergeAltH: IconType;
export declare const CgArrowsMergeAltV: IconType;
export declare const CgArrowsScrollH: IconType;
export declare const CgArrowsScrollV: IconType;
export declare const CgArrowsShrinkH: IconType;
export declare const CgArrowsShrinkV: IconType;
export declare const CgArrowsVAlt: IconType;
export declare const CgArrowsV: IconType;
export declare const CgAssign: IconType;
export declare const CgAsterisk: IconType;
export declare const CgAtlasian: IconType;
export declare const CgAttachment: IconType;
export declare const CgAttribution: IconType;
export declare const CgAwards: IconType;
export declare const CgBackspace: IconType;
export declare const CgBandAid: IconType;
export declare const CgBatteryEmpty: IconType;
export declare const CgBatteryFull: IconType;
export declare const CgBattery: IconType;
export declare const CgBee: IconType;
export declare const CgBell: IconType;
export declare const CgBitbucket: IconType;
export declare const CgBlock: IconType;
export declare const CgBmw: IconType;
export declare const CgBoard: IconType;
export declare const CgBolt: IconType;
export declare const CgBookmark: IconType;
export declare const CgBorderAll: IconType;
export declare const CgBorderBottom: IconType;
export declare const CgBorderLeft: IconType;
export declare const CgBorderRight: IconType;
export declare const CgBorderStyleDashed: IconType;
export declare const CgBorderStyleDotted: IconType;
export declare const CgBorderStyleSolid: IconType;
export declare const CgBorderTop: IconType;
export declare const CgBot: IconType;
export declare const CgBowl: IconType;
export declare const CgBox: IconType;
export declare const CgBoy: IconType;
export declare const CgBrackets: IconType;
export declare const CgBriefcase: IconType;
export declare const CgBrowse: IconType;
export declare const CgBrowser: IconType;
export declare const CgBrush: IconType;
export declare const CgBulb: IconType;
export declare const CgCPlusPlus: IconType;
export declare const CgCalculator: IconType;
export declare const CgCalendarDates: IconType;
export declare const CgCalendarDue: IconType;
export declare const CgCalendarNext: IconType;
export declare const CgCalendarToday: IconType;
export declare const CgCalendarTwo: IconType;
export declare const CgCalendar: IconType;
export declare const CgCalibrate: IconType;
export declare const CgCamera: IconType;
export declare const CgCap: IconType;
export declare const CgCaptions: IconType;
export declare const CgCardClubs: IconType;
export declare const CgCardDiamonds: IconType;
export declare const CgCardHearts: IconType;
export declare const CgCardSpades: IconType;
export declare const CgCarousel: IconType;
export declare const CgCast: IconType;
export declare const CgChanel: IconType;
export declare const CgChart: IconType;
export declare const CgCheckO: IconType;
export declare const CgCheckR: IconType;
export declare const CgCheck: IconType;
export declare const CgChevronDoubleDownO: IconType;
export declare const CgChevronDoubleDownR: IconType;
export declare const CgChevronDoubleDown: IconType;
export declare const CgChevronDoubleLeftO: IconType;
export declare const CgChevronDoubleLeftR: IconType;
export declare const CgChevronDoubleLeft: IconType;
export declare const CgChevronDoubleRightO: IconType;
export declare const CgChevronDoubleRightR: IconType;
export declare const CgChevronDoubleRight: IconType;
export declare const CgChevronDoubleUpO: IconType;
export declare const CgChevronDoubleUpR: IconType;
export declare const CgChevronDoubleUp: IconType;
export declare const CgChevronDownO: IconType;
export declare const CgChevronDownR: IconType;
export declare const CgChevronDown: IconType;
export declare const CgChevronLeftO: IconType;
export declare const CgChevronLeftR: IconType;
export declare const CgChevronLeft: IconType;
export declare const CgChevronRightO: IconType;
export declare const CgChevronRightR: IconType;
export declare const CgChevronRight: IconType;
export declare const CgChevronUpO: IconType;
export declare const CgChevronUpR: IconType;
export declare const CgChevronUp: IconType;
export declare const CgCircleci: IconType;
export declare const CgClapperBoard: IconType;
export declare const CgClipboard: IconType;
export declare const CgCloseO: IconType;
export declare const CgCloseR: IconType;
export declare const CgClose: IconType;
export declare const CgCloud: IconType;
export declare const CgCodeClimate: IconType;
export declare const CgCodeSlash: IconType;
export declare const CgCode: IconType;
export declare const CgCoffee: IconType;
export declare const CgCollage: IconType;
export declare const CgColorBucket: IconType;
export declare const CgColorPicker: IconType;
export declare const CgComedyCentral: IconType;
export declare const CgComment: IconType;
export declare const CgCommunity: IconType;
export declare const CgComponents: IconType;
export declare const CgCompressLeft: IconType;
export declare const CgCompressRight: IconType;
export declare const CgCompressV: IconType;
export declare const CgCompress: IconType;
export declare const CgController: IconType;
export declare const CgCopy: IconType;
export declare const CgCopyright: IconType;
export declare const CgCornerDoubleDownLeft: IconType;
export declare const CgCornerDoubleDownRight: IconType;
export declare const CgCornerDoubleLeftDown: IconType;
export declare const CgCornerDoubleLeftUp: IconType;
export declare const CgCornerDoubleRightDown: IconType;
export declare const CgCornerDoubleRightUp: IconType;
export declare const CgCornerDoubleUpLeft: IconType;
export declare const CgCornerDoubleUpRight: IconType;
export declare const CgCornerDownLeft: IconType;
export declare const CgCornerDownRight: IconType;
export declare const CgCornerLeftDown: IconType;
export declare const CgCornerLeftUp: IconType;
export declare const CgCornerRightDown: IconType;
export declare const CgCornerRightUp: IconType;
export declare const CgCornerUpLeft: IconType;
export declare const CgCornerUpRight: IconType;
export declare const CgCreditCard: IconType;
export declare const CgCrop: IconType;
export declare const CgCross: IconType;
export declare const CgCrowdfire: IconType;
export declare const CgCrown: IconType;
export declare const CgDanger: IconType;
export declare const CgDarkMode: IconType;
export declare const CgData: IconType;
export declare const CgDatabase: IconType;
export declare const CgDebug: IconType;
export declare const CgDesignmodo: IconType;
export declare const CgDesktop: IconType;
export declare const CgDetailsLess: IconType;
export declare const CgDetailsMore: IconType;
export declare const CgDialpad: IconType;
export declare const CgDice1: IconType;
export declare const CgDice2: IconType;
export declare const CgDice3: IconType;
export declare const CgDice4: IconType;
export declare const CgDice5: IconType;
export declare const CgDice6: IconType;
export declare const CgDigitalocean: IconType;
export declare const CgDisc: IconType;
export declare const CgDisplayFlex: IconType;
export declare const CgDisplayFullwidth: IconType;
export declare const CgDisplayGrid: IconType;
export declare const CgDisplaySpacing: IconType;
export declare const CgDistributeHorizontal: IconType;
export declare const CgDistributeVertical: IconType;
export declare const CgDockBottom: IconType;
export declare const CgDockLeft: IconType;
export declare const CgDockRight: IconType;
export declare const CgDockWindow: IconType;
export declare const CgDolby: IconType;
export declare const CgDollar: IconType;
export declare const CgDribbble: IconType;
export declare const CgDrive: IconType;
export declare const CgDropInvert: IconType;
export declare const CgDropOpacity: IconType;
export declare const CgDrop: IconType;
export declare const CgDuplicate: IconType;
export declare const CgEditBlackPoint: IconType;
export declare const CgEditContrast: IconType;
export declare const CgEditExposure: IconType;
export declare const CgEditFade: IconType;
export declare const CgEditFlipH: IconType;
export declare const CgEditFlipV: IconType;
export declare const CgEditHighlight: IconType;
export declare const CgEditMarkup: IconType;
export declare const CgEditMask: IconType;
export declare const CgEditNoise: IconType;
export declare const CgEditShadows: IconType;
export declare const CgEditStraight: IconType;
export declare const CgEditUnmask: IconType;
export declare const CgEject: IconType;
export declare const CgEnter: IconType;
export declare const CgErase: IconType;
export declare const CgEreader: IconType;
export declare const CgEricsson: IconType;
export declare const CgEthernet: IconType;
export declare const CgEuro: IconType;
export declare const CgEventbrite: IconType;
export declare const CgExpand: IconType;
export declare const CgExport: IconType;
export declare const CgExtensionAdd: IconType;
export declare const CgExtensionAlt: IconType;
export declare const CgExtensionRemove: IconType;
export declare const CgExtension: IconType;
export declare const CgExternal: IconType;
export declare const CgEyeAlt: IconType;
export declare const CgEye: IconType;
export declare const CgFacebook: IconType;
export declare const CgFeed: IconType;
export declare const CgFigma: IconType;
export declare const CgFileAdd: IconType;
export declare const CgFileDocument: IconType;
export declare const CgFileRemove: IconType;
export declare const CgFile: IconType;
export declare const CgFilm: IconType;
export declare const CgFilters: IconType;
export declare const CgFlagAlt: IconType;
export declare const CgFlag: IconType;
export declare const CgFolderAdd: IconType;
export declare const CgFolderRemove: IconType;
export declare const CgFolder: IconType;
export declare const CgFontHeight: IconType;
export declare const CgFontSpacing: IconType;
export declare const CgFormatBold: IconType;
export declare const CgFormatCenter: IconType;
export declare const CgFormatColor: IconType;
export declare const CgFormatHeading: IconType;
export declare const CgFormatIndentDecrease: IconType;
export declare const CgFormatIndentIncrease: IconType;
export declare const CgFormatItalic: IconType;
export declare const CgFormatJustify: IconType;
export declare const CgFormatLeft: IconType;
export declare const CgFormatLineHeight: IconType;
export declare const CgFormatRight: IconType;
export declare const CgFormatSeparator: IconType;
export declare const CgFormatSlash: IconType;
export declare const CgFormatStrike: IconType;
export declare const CgFormatText: IconType;
export declare const CgFormatUnderline: IconType;
export declare const CgFormatUppercase: IconType;
export declare const CgFramer: IconType;
export declare const CgGames: IconType;
export declare const CgGenderFemale: IconType;
export declare const CgGenderMale: IconType;
export declare const CgGhostCharacter: IconType;
export declare const CgGhost: IconType;
export declare const CgGift: IconType;
export declare const CgGirl: IconType;
export declare const CgGitBranch: IconType;
export declare const CgGitCommit: IconType;
export declare const CgGitFork: IconType;
export declare const CgGitPull: IconType;
export declare const CgGitter: IconType;
export declare const CgGlassAlt: IconType;
export declare const CgGlass: IconType;
export declare const CgGlobeAlt: IconType;
export declare const CgGlobe: IconType;
export declare const CgGoogleTasks: IconType;
export declare const CgGoogle: IconType;
export declare const CgGym: IconType;
export declare const CgHashtag: IconType;
export declare const CgHeadset: IconType;
export declare const CgHeart: IconType;
export declare const CgHello: IconType;
export declare const CgHomeAlt: IconType;
export declare const CgHomeScreen: IconType;
export declare const CgHome: IconType;
export declare const CgIcecream: IconType;
export declare const CgIfDesign: IconType;
export declare const CgImage: IconType;
export declare const CgImport: IconType;
export declare const CgInbox: IconType;
export declare const CgIndieHackers: IconType;
export declare const CgInfinity: IconType;
export declare const CgInfo: IconType;
export declare const CgInpicture: IconType;
export declare const CgInsertAfterO: IconType;
export declare const CgInsertAfterR: IconType;
export declare const CgInsertAfter: IconType;
export declare const CgInsertBeforeO: IconType;
export declare const CgInsertBeforeR: IconType;
export declare const CgInsertBefore: IconType;
export declare const CgInsights: IconType;
export declare const CgInstagram: IconType;
export declare const CgInternal: IconType;
export declare const CgKey: IconType;
export declare const CgKeyboard: IconType;
export declare const CgKeyhole: IconType;
export declare const CgLaptop: IconType;
export declare const CgLastpass: IconType;
export declare const CgLayoutGridSmall: IconType;
export declare const CgLayoutGrid: IconType;
export declare const CgLayoutList: IconType;
export declare const CgLayoutPin: IconType;
export declare const CgLinear: IconType;
export declare const CgLink: IconType;
export declare const CgListTree: IconType;
export declare const CgList: IconType;
export declare const CgLivePhoto: IconType;
export declare const CgLoadbarAlt: IconType;
export declare const CgLoadbarDoc: IconType;
export declare const CgLoadbarSound: IconType;
export declare const CgLoadbar: IconType;
export declare const CgLockUnlock: IconType;
export declare const CgLock: IconType;
export declare const CgLogIn: IconType;
export declare const CgLogOff: IconType;
export declare const CgLogOut: IconType;
export declare const CgLoupe: IconType;
export declare const CgMagnet: IconType;
export declare const CgMailForward: IconType;
export declare const CgMailOpen: IconType;
export declare const CgMailReply: IconType;
export declare const CgMail: IconType;
export declare const CgMathDivide: IconType;
export declare const CgMathEqual: IconType;
export declare const CgMathMinus: IconType;
export declare const CgMathPercent: IconType;
export declare const CgMathPlus: IconType;
export declare const CgMaximizeAlt: IconType;
export declare const CgMaximize: IconType;
export declare const CgMaze: IconType;
export declare const CgMediaLive: IconType;
export declare const CgMediaPodcast: IconType;
export declare const CgMenuBoxed: IconType;
export declare const CgMenuCake: IconType;
export declare const CgMenuCheese: IconType;
export declare const CgMenuGridO: IconType;
export declare const CgMenuGridR: IconType;
export declare const CgMenuHotdog: IconType;
export declare const CgMenuLeftAlt: IconType;
export declare const CgMenuLeft: IconType;
export declare const CgMenuMotion: IconType;
export declare const CgMenuOreos: IconType;
export declare const CgMenuRightAlt: IconType;
export declare const CgMenuRight: IconType;
export declare const CgMenuRound: IconType;
export declare const CgMenu: IconType;
export declare const CgMergeHorizontal: IconType;
export declare const CgMergeVertical: IconType;
export declare const CgMic: IconType;
export declare const CgMicrobit: IconType;
export declare const CgMicrosoft: IconType;
export declare const CgMiniPlayer: IconType;
export declare const CgMinimizeAlt: IconType;
export declare const CgMinimize: IconType;
export declare const CgModem: IconType;
export declare const CgMonday: IconType;
export declare const CgMoon: IconType;
export declare const CgMoreAlt: IconType;
export declare const CgMoreO: IconType;
export declare const CgMoreR: IconType;
export declare const CgMoreVerticalAlt: IconType;
export declare const CgMoreVerticalO: IconType;
export declare const CgMoreVerticalR: IconType;
export declare const CgMoreVertical: IconType;
export declare const CgMore: IconType;
export declare const CgMouse: IconType;
export declare const CgMoveDown: IconType;
export declare const CgMoveLeft: IconType;
export declare const CgMoveRight: IconType;
export declare const CgMoveTask: IconType;
export declare const CgMoveUp: IconType;
export declare const CgMusicNote: IconType;
export declare const CgMusicSpeaker: IconType;
export declare const CgMusic: IconType;
export declare const CgNametag: IconType;
export declare const CgNotes: IconType;
export declare const CgNotifications: IconType;
export declare const CgNpm: IconType;
export declare const CgOculus: IconType;
export declare const CgOpenCollective: IconType;
export declare const CgOptions: IconType;
export declare const CgOrganisation: IconType;
export declare const CgOverflow: IconType;
export declare const CgPacman: IconType;
export declare const CgPassword: IconType;
export declare const CgPathBack: IconType;
export declare const CgPathCrop: IconType;
export declare const CgPathDivide: IconType;
export declare const CgPathExclude: IconType;
export declare const CgPathFront: IconType;
export declare const CgPathIntersect: IconType;
export declare const CgPathOutline: IconType;
export declare const CgPathTrim: IconType;
export declare const CgPathUnite: IconType;
export declare const CgPatreon: IconType;
export declare const CgPaypal: IconType;
export declare const CgPen: IconType;
export declare const CgPentagonBottomLeft: IconType;
export declare const CgPentagonBottomRight: IconType;
export declare const CgPentagonDown: IconType;
export declare const CgPentagonLeft: IconType;
export declare const CgPentagonRight: IconType;
export declare const CgPentagonTopLeft: IconType;
export declare const CgPentagonTopRight: IconType;
export declare const CgPentagonUp: IconType;
export declare const CgPerformance: IconType;
export declare const CgPexels: IconType;
export declare const CgPhone: IconType;
export declare const CgPhotoscan: IconType;
export declare const CgPiano: IconType;
export declare const CgPill: IconType;
export declare const CgPinAlt: IconType;
export declare const CgPinBottom: IconType;
export declare const CgPinTop: IconType;
export declare const CgPin: IconType;
export declare const CgPlayBackwards: IconType;
export declare const CgPlayButtonO: IconType;
export declare const CgPlayButtonR: IconType;
export declare const CgPlayButton: IconType;
export declare const CgPlayForwards: IconType;
export declare const CgPlayListAdd: IconType;
export declare const CgPlayListCheck: IconType;
export declare const CgPlayListRemove: IconType;
export declare const CgPlayListSearch: IconType;
export declare const CgPlayList: IconType;
export declare const CgPlayPauseO: IconType;
export declare const CgPlayPauseR: IconType;
export declare const CgPlayPause: IconType;
export declare const CgPlayStopO: IconType;
export declare const CgPlayStopR: IconType;
export declare const CgPlayStop: IconType;
export declare const CgPlayTrackNextO: IconType;
export declare const CgPlayTrackNextR: IconType;
export declare const CgPlayTrackNext: IconType;
export declare const CgPlayTrackPrevO: IconType;
export declare const CgPlayTrackPrevR: IconType;
export declare const CgPlayTrackPrev: IconType;
export declare const CgPlug: IconType;
export declare const CgPocket: IconType;
export declare const CgPokemon: IconType;
export declare const CgPolaroid: IconType;
export declare const CgPoll: IconType;
export declare const CgPresentation: IconType;
export declare const CgPrinter: IconType;
export declare const CgProductHunt: IconType;
export declare const CgProfile: IconType;
export declare const CgPullClear: IconType;
export declare const CgPushChevronDownO: IconType;
export declare const CgPushChevronDownR: IconType;
export declare const CgPushChevronDown: IconType;
export declare const CgPushChevronLeftO: IconType;
export declare const CgPushChevronLeftR: IconType;
export declare const CgPushChevronLeft: IconType;
export declare const CgPushChevronRightO: IconType;
export declare const CgPushChevronRightR: IconType;
export declare const CgPushChevronRight: IconType;
export declare const CgPushChevronUpO: IconType;
export declare const CgPushChevronUpR: IconType;
export declare const CgPushChevronUp: IconType;
export declare const CgPushDown: IconType;
export declare const CgPushLeft: IconType;
export declare const CgPushRight: IconType;
export declare const CgPushUp: IconType;
export declare const CgQr: IconType;
export declare const CgQuoteO: IconType;
export declare const CgQuote: IconType;
export declare const CgRadioCheck: IconType;
export declare const CgRadioChecked: IconType;
export declare const CgRatio: IconType;
export declare const CgRead: IconType;
export declare const CgReadme: IconType;
export declare const CgRecord: IconType;
export declare const CgRedo: IconType;
export declare const CgRemote: IconType;
export declare const CgRemoveR: IconType;
export declare const CgRemove: IconType;
export declare const CgRename: IconType;
export declare const CgReorder: IconType;
export declare const CgRepeat: IconType;
export declare const CgRing: IconType;
export declare const CgRowFirst: IconType;
export declare const CgRowLast: IconType;
export declare const CgRuler: IconType;
export declare const CgSandClock: IconType;
export declare const CgScan: IconType;
export declare const CgScreenMirror: IconType;
export declare const CgScreenShot: IconType;
export declare const CgScreenWide: IconType;
export declare const CgScreen: IconType;
export declare const CgScrollH: IconType;
export declare const CgScrollV: IconType;
export declare const CgSearchFound: IconType;
export declare const CgSearchLoading: IconType;
export declare const CgSearch: IconType;
export declare const CgSelectO: IconType;
export declare const CgSelectR: IconType;
export declare const CgSelect: IconType;
export declare const CgServer: IconType;
export declare const CgServerless: IconType;
export declare const CgShapeCircle: IconType;
export declare const CgShapeHalfCircle: IconType;
export declare const CgShapeHexagon: IconType;
export declare const CgShapeRhombus: IconType;
export declare const CgShapeSquare: IconType;
export declare const CgShapeTriangle: IconType;
export declare const CgShapeZigzag: IconType;
export declare const CgShare: IconType;
export declare const CgShield: IconType;
export declare const CgShoppingBag: IconType;
export declare const CgShoppingCart: IconType;
export declare const CgShortcut: IconType;
export declare const CgShutterstock: IconType;
export declare const CgSidebarOpen: IconType;
export declare const CgSidebarRight: IconType;
export declare const CgSidebar: IconType;
export declare const CgSignal: IconType;
export declare const CgSize: IconType;
export declare const CgSketch: IconType;
export declare const CgSlack: IconType;
export declare const CgSleep: IconType;
export declare const CgSmartHomeBoiler: IconType;
export declare const CgSmartHomeCooker: IconType;
export declare const CgSmartHomeHeat: IconType;
export declare const CgSmartHomeLight: IconType;
export declare const CgSmartHomeRefrigerator: IconType;
export declare const CgSmartHomeWashMachine: IconType;
export declare const CgSmartphoneChip: IconType;
export declare const CgSmartphoneRam: IconType;
export declare const CgSmartphoneShake: IconType;
export declare const CgSmartphone: IconType;
export declare const CgSmileMouthOpen: IconType;
export declare const CgSmileNeutral: IconType;
export declare const CgSmileNoMouth: IconType;
export declare const CgSmileNone: IconType;
export declare const CgSmileSad: IconType;
export declare const CgSmileUpside: IconType;
export declare const CgSmile: IconType;
export declare const CgSoftwareDownload: IconType;
export declare const CgSoftwareUpload: IconType;
export declare const CgSortAz: IconType;
export declare const CgSortZa: IconType;
export declare const CgSpaceBetweenV: IconType;
export declare const CgSpaceBetween: IconType;
export declare const CgSpectrum: IconType;
export declare const CgSpinnerAlt: IconType;
export declare const CgSpinnerTwoAlt: IconType;
export declare const CgSpinnerTwo: IconType;
export declare const CgSpinner: IconType;
export declare const CgSquare: IconType;
export declare const CgStack: IconType;
export declare const CgStark: IconType;
export declare const CgStopwatch: IconType;
export declare const CgStories: IconType;
export declare const CgStudio: IconType;
export declare const CgStyle: IconType;
export declare const CgSun: IconType;
export declare const CgSupport: IconType;
export declare const CgSwapVertical: IconType;
export declare const CgSwap: IconType;
export declare const CgSweden: IconType;
export declare const CgSwiss: IconType;
export declare const CgSync: IconType;
export declare const CgTab: IconType;
export declare const CgTag: IconType;
export declare const CgTally: IconType;
export declare const CgTapDouble: IconType;
export declare const CgTapSingle: IconType;
export declare const CgTemplate: IconType;
export declare const CgTennis: IconType;
export declare const CgTerminal: IconType;
export declare const CgTerrain: IconType;
export declare const CgThermometer: IconType;
export declare const CgThermostat: IconType;
export declare const CgTikcode: IconType;
export declare const CgTime: IconType;
export declare const CgTimelapse: IconType;
export declare const CgTimer: IconType;
export declare const CgToday: IconType;
export declare const CgToggleOff: IconType;
export declare const CgToggleOn: IconType;
export declare const CgToggleSquareOff: IconType;
export declare const CgToggleSquare: IconType;
export declare const CgToolbarBottom: IconType;
export declare const CgToolbarLeft: IconType;
export declare const CgToolbarRight: IconType;
export declare const CgToolbarTop: IconType;
export declare const CgToolbox: IconType;
export declare const CgTouchpad: IconType;
export declare const CgTrack: IconType;
export declare const CgTranscript: IconType;
export declare const CgTrashEmpty: IconType;
export declare const CgTrash: IconType;
export declare const CgTree: IconType;
export declare const CgTrees: IconType;
export declare const CgTrello: IconType;
export declare const CgTrendingDown: IconType;
export declare const CgTrending: IconType;
export declare const CgTrophy: IconType;
export declare const CgTv: IconType;
export declare const CgTwilio: IconType;
export declare const CgTwitter: IconType;
export declare const CgUiKit: IconType;
export declare const CgUmbrella: IconType;
export declare const CgUnavailable: IconType;
export declare const CgUnblock: IconType;
export declare const CgUndo: IconType;
export declare const CgUnfold: IconType;
export declare const CgUnsplash: IconType;
export declare const CgUsbC: IconType;
export declare const CgUsb: IconType;
export declare const CgUserAdd: IconType;
export declare const CgUserList: IconType;
export declare const CgUserRemove: IconType;
export declare const CgUser: IconType;
export declare const CgUserlane: IconType;
export declare const CgVercel: IconType;
export declare const CgViewCols: IconType;
export declare const CgViewComfortable: IconType;
export declare const CgViewDay: IconType;
export declare const CgViewGrid: IconType;
export declare const CgViewList: IconType;
export declare const CgViewMonth: IconType;
export declare const CgViewSplit: IconType;
export declare const CgVinyl: IconType;
export declare const CgVoicemailO: IconType;
export declare const CgVoicemailR: IconType;
export declare const CgVoicemail: IconType;
export declare const CgVolume: IconType;
export declare const CgWebcam: IconType;
export declare const CgWebsite: IconType;
export declare const CgWindows: IconType;
export declare const CgWorkAlt: IconType;
export declare const CgYinyang: IconType;
export declare const CgYoutube: IconType;
export declare const CgZoomIn: IconType;
export declare const CgZoomOut: IconType;
