.weather-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 1px 0px rgba(255, 255, 255, 0.8) inset,
    0 -1px 0px rgba(0, 0, 0, 0.05) inset;
  padding: 2.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 650px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.weather-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px 24px 0 0;
}

.weather-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 1px 0px rgba(255, 255, 255, 0.9) inset,
    0 -1px 0px rgba(0, 0, 0, 0.05) inset;
}

.weather-card.compact {
  padding: 2rem;
  max-width: 380px;
  border-radius: 20px;
}

.weather-card.compact::before {
  border-radius: 20px 20px 0 0;
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.location-info {
  flex: 1;
}

.city-name {
  font-size: 1.75rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.compact .city-name {
  font-size: 1.25rem;
  margin-bottom: 0.125rem;
}

.location-icon {
  color: #667eea;
  font-size: 1.5rem;
}

.country-name {
  color: #718096;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.coordinates {
  color: #a0aec0;
  font-size: 0.85rem;
}

.favorite-btn {
  background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  border-radius: 50%;
  width: 52px;
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #64748b;
  font-size: 1.3rem;
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.1),
    0 1px 0px rgba(255, 255, 255, 0.8) inset;
  position: relative;
  overflow: hidden;
}

.favorite-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.2) 0%, transparent 70%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.favorite-btn:hover {
  border-color: #ff6b6b;
  color: #ff6b6b;
  transform: scale(1.1);
  box-shadow:
    0 8px 16px rgba(255, 107, 107, 0.3),
    0 1px 0px rgba(255, 255, 255, 0.9) inset;
}

.favorite-btn:hover::before {
  width: 100%;
  height: 100%;
}

.favorite-btn.active {
  background: linear-gradient(145deg, #ff6b6b 0%, #ff5252 100%);
  border-color: #ff6b6b;
  color: white;
  box-shadow:
    0 8px 16px rgba(255, 107, 107, 0.4),
    0 1px 0px rgba(255, 255, 255, 0.3) inset;
}

.favorite-btn.active::before {
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  width: 100%;
  height: 100%;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.weather-main.compact {
  margin-bottom: 1.5rem;
}

.weather-icon-container {
  flex-shrink: 0;
}

.weather-icon {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.weather-icon.large {
  width: 120px;
  height: 120px;
}

.weather-icon-temp {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.temperature-info {
  flex: 1;
}

.current-temp {
  font-size: 3rem;
  font-weight: 700;
  color: #2d3748;
  display: block;
  line-height: 1;
}

.compact .current-temp {
  font-size: 2rem;
}

.feels-like {
  color: #718096;
  font-size: 0.95rem;
  display: block;
  margin-top: 0.25rem;
}

.weather-desc {
  color: #4a5568;
  font-size: 1.1rem;
  font-weight: 500;
  text-transform: capitalize;
  display: block;
  margin-top: 0.5rem;
}

.compact .weather-desc {
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.temp-range {
  display: flex;
  gap: 1rem;
  margin-top: 0.75rem;
}

.temp-min,
.temp-max {
  color: #718096;
  font-size: 0.95rem;
  font-weight: 500;
}

.weather-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.weather-details.compact {
  flex-direction: row;
  gap: 1rem;
  justify-content: space-around;
}

.detail-row {
  display: flex;
  gap: 2rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.compact .detail-item {
  flex-direction: column;
  gap: 0.25rem;
  text-align: center;
}

.detail-icon {
  color: #667eea;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.compact .detail-icon {
  font-size: 1rem;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.detail-label {
  color: #718096;
  font-size: 0.85rem;
  font-weight: 500;
}

.detail-value {
  color: #2d3748;
  font-size: 0.95rem;
  font-weight: 600;
}

.compact .detail-value {
  font-size: 0.85rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.25rem;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer-progress 2s infinite;
}

@keyframes shimmer-progress {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.weather-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.last-updated {
  color: #a0aec0;
  font-size: 0.8rem;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .weather-card {
    padding: 1.5rem;
  }
  
  .weather-main {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .weather-icon-temp {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .detail-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .city-name {
    font-size: 1.5rem;
  }
  
  .current-temp {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .weather-card {
    padding: 1rem;
  }
  
  .weather-header {
    margin-bottom: 1.5rem;
  }
  
  .city-name {
    font-size: 1.25rem;
  }
  
  .current-temp {
    font-size: 2rem;
  }
  
  .weather-icon.large {
    width: 100px;
    height: 100px;
  }
  
  .favorite-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .detail-item {
    padding: 0.75rem;
    background: #f7fafc;
    border-radius: 8px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .weather-card {
    background: #2d3748;
    color: white;
  }
  
  .city-name {
    color: #e2e8f0;
  }
  
  .country-name {
    color: #a0aec0;
  }
  
  .coordinates {
    color: #718096;
  }
  
  .current-temp {
    color: #e2e8f0;
  }
  
  .feels-like {
    color: #a0aec0;
  }
  
  .weather-desc {
    color: #cbd5e0;
  }
  
  .temp-min,
  .temp-max {
    color: #a0aec0;
  }
  
  .detail-label {
    color: #a0aec0;
  }
  
  .detail-value {
    color: #e2e8f0;
  }
  
  .weather-footer {
    border-top-color: #4a5568;
  }
  
  .last-updated {
    color: #718096;
  }
  
  .favorite-btn {
    border-color: #4a5568;
    color: #a0aec0;
  }
  
  .favorite-btn:hover {
    border-color: #ff6b6b;
    color: #ff6b6b;
  }
  
  .detail-item {
    background: #4a5568;
  }
}
